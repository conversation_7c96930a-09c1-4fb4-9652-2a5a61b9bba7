# match.py

import spacy
from typing import List, Dict
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

nlp = spacy.load("en_core_web_sm")


def clean_text(text):
    doc = nlp(text.lower())
    tokens = [token.lemma_ for token in doc if not token.is_stop and not token.is_punct and token.is_alpha]
    return " ".join(tokens)


def match_jobs_to_resume(jobs: List[Dict], resume_text: str, top_k: int = 10) -> List[Dict]:
    resume_clean = clean_text(resume_text)
    job_corpus = [clean_text(job['title'] + ' ' + job.get('description', '')) for job in jobs]
    vectorizer = TfidfVectorizer()
    vectors = vectorizer.fit_transform([resume_clean] + job_corpus)
    resume_vec = vectors[0]
    job_vecs = vectors[1:]
    sims = cosine_similarity(resume_vec, job_vecs).flatten()

    for job, sim in zip(jobs, sims):
        job['similarity'] = round(float(sim), 4)

    return sorted(jobs, key=lambda x: x['similarity'], reverse=True)[:top_k]
