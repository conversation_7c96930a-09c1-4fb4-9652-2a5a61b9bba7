# match.py

import string
from typing import List, Dict
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# Simple stopwords list
STOPWORDS = {
    'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from', 'has', 'he',
    'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the', 'to', 'was', 'will', 'with',
    'i', 'you', 'we', 'they', 'this', 'these', 'those', 'or', 'but', 'if', 'then',
    'can', 'could', 'should', 'would', 'may', 'might', 'must', 'shall', 'do', 'does',
    'did', 'have', 'had', 'been', 'being', 'am', 'were', 'my', 'your', 'his', 'her',
    'our', 'their', 'me', 'him', 'us', 'them', 'what', 'when', 'where', 'who', 'why',
    'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such'
}


def clean_text(text):
    """Simple text cleaning without spaCy."""
    if not text:
        return ""

    # Convert to lowercase
    text = text.lower()

    # Remove punctuation
    text = text.translate(str.maketrans('', '', string.punctuation))

    # Split into words and remove stopwords
    words = text.split()
    words = [word for word in words if word not in STOPWORDS and len(word) > 2]

    return " ".join(words)


def match_jobs_to_resume(jobs: List[Dict], resume_text: str, top_k: int = 10) -> List[Dict]:
    """Match jobs to resume using TF-IDF similarity."""
    if not jobs or not resume_text:
        return jobs[:top_k]

    try:
        resume_clean = clean_text(resume_text)
        job_corpus = [clean_text(job['title'] + ' ' + job.get('description', '')) for job in jobs]

        # Filter out empty strings
        all_texts = [resume_clean] + job_corpus
        if not any(text.strip() for text in all_texts):
            # If all texts are empty, return jobs with 0 similarity
            for job in jobs:
                job['similarity'] = 0.0
            return jobs[:top_k]

        vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        vectors = vectorizer.fit_transform(all_texts)

        resume_vec = vectors[0]
        job_vecs = vectors[1:]
        sims = cosine_similarity(resume_vec, job_vecs).flatten()

        for job, sim in zip(jobs, sims):
            job['similarity'] = round(float(sim), 4)

        return sorted(jobs, key=lambda x: x['similarity'], reverse=True)[:top_k]

    except Exception as e:
        # If matching fails, return jobs with 0 similarity
        for job in jobs:
            job['similarity'] = 0.0
        return jobs[:top_k]
