# Job Hunter 🚀

An AI-powered job search application built with Django that helps you find, analyze, and apply for jobs with intelligent matching and automated cover letter generation.

## Features

### 🔍 Smart Job Search
- **LinkedIn Job Scraping**: Automatically scrape job listings from LinkedIn
- **Advanced Filtering**: Search by keywords, locations, and experience levels
- **Intelligent Caching**: Avoid duplicate scraping with built-in URL caching

### 🧠 AI-Powered Matching
- **Resume Analysis**: Upload and manage multiple resumes
- **Similarity Scoring**: Get personalized job recommendations based on your resume
- **Keyword Extraction**: Automatic analysis of job descriptions and requirements

### 📝 Cover Letter Generation
- **AI-Generated Letters**: Create personalized cover letters using OpenAI GPT
- **Job-Specific Content**: Tailored content based on job description and your resume
- **Multiple Resume Support**: Generate different letters for different resume versions

### 📊 Application Tracking
- **Status Management**: Track application progress from saved to offer
- **Application History**: Keep detailed records of all your job applications
- **Search History**: Review and repeat previous searches

### 👤 User Management
- **Profile Management**: Store personal information and preferences
- **Resume Library**: Upload and manage multiple resume versions
- **Preference Settings**: Set preferred locations and job keywords

## Quick Start

### Automated Setup (Recommended)

1. **Clone and setup**
   ```bash
   git clone <repository-url>
   cd job-hunt
   python -m venv env
   env\Scripts\activate  # On Windows
   # source env/bin/activate  # On macOS/Linux
   python setup.py
   ```

2. **Start the application**
   ```bash
   cd job_hunter
   python manage.py runserver
   ```

3. **Access the application**
   - Open: http://127.0.0.1:8000
   - Demo login: `demo` / `demo123`
   - Admin login: `admin` / (set password on first login)

### Manual Setup

1. **Prerequisites**
   - Python 3.8+
   - Chrome browser (for web scraping)
   - Virtual environment (recommended)

2. **Installation**
   ```bash
   git clone <repository-url>
   cd job-hunt
   python -m venv env
   env\Scripts\activate  # Windows
   pip install -r requirements.txt
   ```

3. **Database setup**
   ```bash
   cd job_hunter
   python manage.py makemigrations
   python manage.py migrate
   python manage.py create_sample_data
   python manage.py createsuperuser
   ```

4. **Run the application**
   ```bash
   python manage.py runserver
   ```

## Configuration

### Environment Variables

Create a `.env` file in the project root:

```env
# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# OpenAI API Key (for cover letter generation)
OPENAI_API_KEY=your-openai-api-key-here
```

### OpenAI Setup (Optional)

1. Get an API key from [OpenAI](https://platform.openai.com/api-keys)
2. Add it to your `.env` file: `OPENAI_API_KEY=your-key-here`
3. Ensure you have sufficient credits for API usage

**Note:** Cover letter generation will be disabled without an OpenAI API key.

## Current Status

### ✅ Completed Features

- **Full Django Application**: Complete web application with user authentication
- **Job Search Interface**: Beautiful, responsive web interface for job searching
- **LinkedIn Job Scraping**: Automated job scraping from LinkedIn (requires Chrome)
- **Resume Management**: Upload, manage, and store multiple resumes
- **Job Matching**: AI-powered job-resume similarity scoring using TF-IDF
- **Application Tracking**: Track job applications with status management
- **User Profiles**: Extended user profiles with preferences
- **Admin Interface**: Full Django admin for data management
- **Sample Data**: Pre-loaded demo data for testing
- **Comprehensive Tests**: 12+ test cases covering models, views, and functionality
- **Documentation**: Complete setup and usage documentation

### 🔧 Working Components

- **Models**: Job, Resume, JobApplication, UserProfile, SearchHistory
- **Views**: 15+ views covering all major functionality
- **Templates**: Responsive Bootstrap-based UI
- **URL Routing**: Complete URL structure
- **Database**: SQLite with proper migrations
- **Management Commands**: Custom commands for setup and data creation

### ⚠️ Known Limitations

- **spaCy Dependency**: Removed due to Windows compilation issues (using simpler text processing)
- **OpenAI Integration**: Requires API key for cover letter generation
- **LinkedIn Scraping**: May be blocked by LinkedIn's anti-bot measures
- **Chrome Dependency**: Requires Chrome browser for web scraping

## Usage

### 1. Job Search
- Navigate to the search page
- Enter keywords (e.g., "Python Developer, Data Scientist")
- Enter locations (e.g., "New York, San Francisco")
- Select number of pages to scrape
- Choose a resume for matching (optional)

### 2. Resume Management
- Upload your resume(s) via the Resume section
- Add both file uploads and text content
- Mark your active resume for job matching

### 3. Application Tracking
- Save interesting jobs to your applications
- Update application status as you progress
- Add notes and track important details

### 4. Cover Letter Generation
- Select a job and resume combination
- Generate AI-powered cover letters
- Edit and customize as needed

## Project Structure

```
job-hunt/
├── job_hunter/                 # Django project
│   ├── job_hunter/            # Project settings
│   │   ├── settings.py        # Django configuration
│   │   ├── urls.py           # Main URL routing
│   │   └── ...
│   ├── jobapp/               # Main application
│   │   ├── models.py         # Database models
│   │   ├── views.py          # View functions
│   │   ├── urls.py           # App URL routing
│   │   ├── admin.py          # Admin configuration
│   │   ├── scraper.py        # Job scraping logic
│   │   ├── analyzer.py       # Job analysis
│   │   ├── match.py          # Resume matching
│   │   ├── generator.py      # Cover letter generation
│   │   ├── pipeline.py       # Main processing pipeline
│   │   └── templates/        # HTML templates
│   └── manage.py             # Django management
├── requirements.txt          # Python dependencies
├── .env.example             # Environment template
└── README.md               # This file
```

## API Endpoints

- `POST /api/match-jobs/` - Match jobs with resume
- `GET /api/job-stats/` - Get user statistics

## Technologies Used

- **Backend**: Django 5.2.4
- **Database**: SQLite (default) / PostgreSQL (production)
- **Web Scraping**: Selenium WebDriver
- **NLP**: spaCy, scikit-learn
- **AI**: OpenAI GPT API
- **Frontend**: Bootstrap 5, HTML/CSS/JavaScript

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Troubleshooting

### Common Issues

1. **ChromeDriver Issues**
   - Ensure Chrome browser is installed
   - Selenium will automatically manage ChromeDriver

2. **spaCy Model Missing**
   ```bash
   python -m spacy download en_core_web_sm
   ```

3. **OpenAI API Errors**
   - Check your API key is valid
   - Ensure you have sufficient credits
   - Verify the API key in your `.env` file

4. **LinkedIn Scraping Issues**
   - LinkedIn may block automated requests
   - Use reasonable delays between requests
   - Consider using VPN if blocked

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the Django logs
3. Create an issue in the repository

---

**Happy Job Hunting! 🎯**
