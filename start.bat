@echo off
echo.
echo ========================================
echo    Job Hunter - Django Application
echo ========================================
echo.

REM Check if virtual environment exists
if not exist "env\Scripts\activate.bat" (
    echo Virtual environment not found. Please run setup.py first.
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call env\Scripts\activate.bat

REM Check if database exists
if not exist "job_hunter\db.sqlite3" (
    echo Database not found. Running setup...
    python setup.py
)

REM Start the Django development server
echo.
echo Starting Django development server...
echo.
echo Application will be available at: http://127.0.0.1:8000
echo.
echo Demo credentials:
echo   Username: demo
echo   Password: demo123
echo.
echo Press Ctrl+C to stop the server
echo.

cd job_hunter
python manage.py runserver

pause
