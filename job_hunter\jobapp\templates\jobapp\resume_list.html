{% extends 'jobapp/base.html' %}

{% block title %}My Resumes - Job Hunter{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2>
                <i class="fas fa-file-alt"></i> My Resumes
            </h2>
            <a href="{% url 'jobapp:resume_upload' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Upload New Resume
            </a>
        </div>
    </div>
</div>

{% if resumes %}
<div class="row">
    {% for resume in resumes %}
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h5 class="card-title">{{ resume.title }}</h5>
                    {% if resume.is_active %}
                        <span class="badge bg-success">Active</span>
                    {% else %}
                        <span class="badge bg-secondary">Inactive</span>
                    {% endif %}
                </div>
                
                <p class="card-text text-muted">
                    <small>
                        <i class="fas fa-calendar"></i> 
                        Created: {{ resume.created_at|date:"M d, Y" }}
                        <br>
                        <i class="fas fa-edit"></i> 
                        Updated: {{ resume.updated_at|date:"M d, Y H:i" }}
                    </small>
                </p>
                
                {% if resume.content %}
                <p class="card-text">
                    {{ resume.content|truncatewords:20 }}
                </p>
                {% endif %}
                
                <div class="d-flex justify-content-between align-items-center">
                    <div class="btn-group" role="group">
                        <a href="{% url 'jobapp:resume_detail' resume.id %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="{% url 'jobapp:resume_edit' resume.id %}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                    </div>
                    
                    <small class="text-muted">
                        {{ resume.applications.count }} application{{ resume.applications.count|pluralize }}
                    </small>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="row">
    <div class="col-lg-8 mx-auto text-center">
        <div class="card">
            <div class="card-body">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <h4>No resumes found</h4>
                <p class="text-muted">
                    Upload your first resume to start getting personalized job recommendations 
                    and generate cover letters.
                </p>
                <a href="{% url 'jobapp:resume_upload' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Upload Your First Resume
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
