from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from jobapp.models import Job, Resume, JobApplication, UserProfile


class Command(BaseCommand):
    help = 'Create sample data for testing the application'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create sample user
        user, created = User.objects.get_or_create(
            username='demo',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Demo',
                'last_name': 'User'
            }
        )
        if created:
            user.set_password('demo123')
            user.save()
            self.stdout.write(f'Created user: {user.username}')
        
        # Create user profile
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'phone': '************',
                'preferred_locations': 'New York, San Francisco, Seattle',
                'preferred_keywords': 'Python, Django, React, JavaScript'
            }
        )
        if created:
            self.stdout.write('Created user profile')
        
        # Create sample resume
        resume, created = Resume.objects.get_or_create(
            user=user,
            title='Software Developer Resume',
            defaults={
                'content': '''
John <PERSON>
Software Developer

EXPERIENCE:
- 5+ years of Python development experience
- Expert in Django web framework
- Proficient in React and JavaScript
- Experience with AWS cloud services
- Strong background in SQL and PostgreSQL
- Agile/Scrum methodology experience

SKILLS:
- Programming: Python, JavaScript, HTML, CSS
- Frameworks: Django, React, Flask
- Databases: PostgreSQL, MySQL, MongoDB
- Cloud: AWS, Docker, Kubernetes
- Tools: Git, Jenkins, JIRA

EDUCATION:
- Bachelor's in Computer Science
- Various online certifications in web development
                '''.strip()
            }
        )
        if created:
            self.stdout.write('Created sample resume')
        
        # Create sample jobs
        sample_jobs = [
            {
                'title': 'Senior Python Developer',
                'company': 'TechCorp Inc.',
                'location': 'New York, NY',
                'url': 'https://example.com/job/1',
                'description': 'We are looking for a Senior Python Developer with Django experience to join our team. Must have 5+ years of experience with Python and web development.',
                'keywords': ['python', 'django', 'web development', 'senior', 'backend']
            },
            {
                'title': 'Full Stack Developer',
                'company': 'StartupXYZ',
                'location': 'San Francisco, CA',
                'url': 'https://example.com/job/2',
                'description': 'Join our startup as a Full Stack Developer! We need someone with React, Node.js, and Python skills. Great opportunity for growth.',
                'keywords': ['react', 'nodejs', 'python', 'fullstack', 'startup']
            },
            {
                'title': 'Django Developer',
                'company': 'WebSolutions LLC',
                'location': 'Seattle, WA',
                'url': 'https://example.com/job/3',
                'description': 'Django Developer position available. Work on exciting web applications using Django, PostgreSQL, and AWS. Remote work available.',
                'keywords': ['django', 'python', 'postgresql', 'aws', 'remote']
            },
            {
                'title': 'Data Scientist',
                'company': 'DataCorp',
                'location': 'Boston, MA',
                'url': 'https://example.com/job/4',
                'description': 'Data Scientist role focusing on machine learning and analytics. Python, pandas, scikit-learn experience required.',
                'keywords': ['python', 'data science', 'machine learning', 'pandas', 'analytics']
            },
            {
                'title': 'Frontend Developer',
                'company': 'DesignStudio',
                'location': 'Austin, TX',
                'url': 'https://example.com/job/5',
                'description': 'Frontend Developer specializing in React and modern JavaScript. Work with designers to create amazing user experiences.',
                'keywords': ['react', 'javascript', 'frontend', 'ui', 'css']
            }
        ]
        
        created_jobs = []
        for job_data in sample_jobs:
            job, created = Job.objects.get_or_create(
                url=job_data['url'],
                defaults={
                    'title': job_data['title'],
                    'company': job_data['company'],
                    'location': job_data['location'],
                    'description': job_data['description'],
                    'keywords': job_data['keywords']
                }
            )
            if created:
                created_jobs.append(job)
                self.stdout.write(f'Created job: {job.title} at {job.company}')
        
        # Create sample applications
        if created_jobs:
            for i, job in enumerate(created_jobs[:3]):  # Apply to first 3 jobs
                status_choices = ['saved', 'applied', 'interview']
                application, created = JobApplication.objects.get_or_create(
                    user=user,
                    job=job,
                    defaults={
                        'resume': resume,
                        'status': status_choices[i],
                        'similarity_score': 0.8 - (i * 0.1),  # Decreasing similarity
                        'notes': f'Interested in this {job.title} position. Good match for my skills.'
                    }
                )
                if created:
                    self.stdout.write(f'Created application for: {job.title}')
        
        self.stdout.write(
            self.style.SUCCESS('Successfully created sample data!')
        )
        self.stdout.write(
            self.style.SUCCESS('Demo user credentials: username=demo, password=demo123')
        )
