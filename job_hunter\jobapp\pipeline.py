# pipeline.py

from .scraper import scrape_jobs
from .match import match_jobs_to_resume
from .analyzer import analyze_jobs
from .generator import generate_cover_letter


def run_pipeline(keywords, locations, resume_text, pages=1, top_k=5):
    print("Scraping jobs...")
    jobs = scrape_jobs(keywords, locations, pages)

    print("Analyzing jobs...")
    jobs = analyze_jobs(jobs)

    print("Matching jobs to resume...")
    top_jobs = match_jobs_to_resume(jobs, resume_text, top_k)

    print("Generating cover letters...")
    for job in top_jobs:
        cover_letter = generate_cover_letter(job, resume_text)
        job['cover_letter'] = cover_letter

    return top_jobs
