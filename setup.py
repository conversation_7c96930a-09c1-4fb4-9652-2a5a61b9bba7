#!/usr/bin/env python3
"""
Job Hunter Setup Script
Automated setup for the Job Hunter Django application
"""

import os
import sys
import subprocess
import platform

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during {description}:")
        print(f"Command: {command}")
        print(f"Error: {e.stderr}")
        return None

def main():
    print("🚀 Job Hunter Setup Script")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('job_hunter/manage.py'):
        print("❌ Error: Please run this script from the project root directory")
        sys.exit(1)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version} detected")
    print(f"✅ Platform: {platform.system()}")
    
    # Install dependencies
    if not run_command("pip install -r requirements.txt", "Installing dependencies"):
        print("⚠️  Warning: Some dependencies failed to install. Continuing anyway...")
    
    # Create directories
    os.makedirs("job_hunter/static", exist_ok=True)
    os.makedirs("job_hunter/media", exist_ok=True)
    print("✅ Created static and media directories")
    
    # Run migrations
    if not run_command("python job_hunter/manage.py makemigrations", "Creating migrations"):
        print("❌ Failed to create migrations")
        sys.exit(1)
    
    if not run_command("python job_hunter/manage.py migrate", "Running migrations"):
        print("❌ Failed to run migrations")
        sys.exit(1)
    
    # Create sample data
    if run_command("python job_hunter/manage.py create_sample_data", "Creating sample data"):
        print("✅ Sample data created")
        print("   Demo user: username=demo, password=demo123")
    
    # Create superuser (optional)
    print("\n📝 Creating superuser...")
    print("   Default superuser: username=admin, email=<EMAIL>")
    print("   You can change the password after first login")
    
    # Run tests
    if run_command("python job_hunter/manage.py test jobapp", "Running tests"):
        print("✅ All tests passed")
    else:
        print("⚠️  Some tests failed, but setup continues")
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Activate your virtual environment (if not already active)")
    print("2. Set up your environment variables in .env file")
    print("3. Add your OpenAI API key for cover letter generation")
    print("4. Run: python job_hunter/manage.py runserver")
    print("5. Visit: http://127.0.0.1:8000")
    print("\n🔑 Login credentials:")
    print("   Demo user: demo / demo123")
    print("   Admin user: admin / (set password on first login)")

if __name__ == "__main__":
    main()
