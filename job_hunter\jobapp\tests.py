from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from .models import Job, Resume, JobApplication, UserProfile
from .analyzer import extract_keywords, analyze_jobs
from .match import match_jobs_to_resume


class ModelTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_user_profile_creation(self):
        """Test UserProfile model creation."""
        profile = UserProfile.objects.create(
            user=self.user,
            phone='************',
            preferred_locations='New York, San Francisco',
            preferred_keywords='Python, Django'
        )
        self.assertEqual(str(profile), "testuser's Profile")

    def test_resume_creation(self):
        """Test Resume model creation."""
        resume = Resume.objects.create(
            user=self.user,
            title='Software Developer Resume',
            content='Experienced Python developer with Django skills.'
        )
        self.assertEqual(str(resume), 'testuser - Software Developer Resume')
        self.assertTrue(resume.is_active)

    def test_job_creation(self):
        """Test Job model creation."""
        job = Job.objects.create(
            title='Python Developer',
            company='Tech Corp',
            location='New York, NY',
            url='https://example.com/job/123',
            description='Looking for a Python developer with Django experience.'
        )
        self.assertEqual(str(job), 'Python Developer at Tech Corp')
        self.assertTrue(job.is_active)

    def test_job_application_creation(self):
        """Test JobApplication model creation."""
        resume = Resume.objects.create(
            user=self.user,
            title='Test Resume',
            content='Test content'
        )
        job = Job.objects.create(
            title='Test Job',
            company='Test Company',
            location='Test Location',
            url='https://example.com/job/test'
        )
        application = JobApplication.objects.create(
            user=self.user,
            job=job,
            resume=resume,
            status='saved'
        )
        self.assertEqual(str(application), 'testuser - Test Job (saved)')
        self.assertEqual(application.status, 'saved')


class AnalyzerTests(TestCase):
    def test_extract_keywords(self):
        """Test keyword extraction functionality."""
        text = "Python developer with Django and React experience. AWS and Docker skills required."
        keywords = extract_keywords(text, top_n=5)

        self.assertIsInstance(keywords, list)
        self.assertGreater(len(keywords), 0)
        # Should prioritize technical keywords
        self.assertIn('python', [k.lower() for k in keywords])

    def test_analyze_jobs(self):
        """Test job analysis functionality."""
        jobs = [
            {
                'title': 'Python Developer',
                'description': 'Looking for Python developer with Django experience',
                'company': 'Tech Corp',
                'location': 'NYC'
            }
        ]
        analyzed = analyze_jobs(jobs)

        self.assertEqual(len(analyzed), 1)
        self.assertIn('keywords', analyzed[0])
        self.assertIsInstance(analyzed[0]['keywords'], list)


class MatchingTests(TestCase):
    def test_match_jobs_to_resume(self):
        """Test job matching functionality."""
        jobs = [
            {
                'id': 1,
                'title': 'Python Developer',
                'description': 'Python Django developer needed',
                'company': 'Tech Corp',
                'location': 'NYC'
            },
            {
                'id': 2,
                'title': 'Java Developer',
                'description': 'Java Spring developer needed',
                'company': 'Java Corp',
                'location': 'SF'
            }
        ]
        resume_text = "Experienced Python developer with Django and Flask experience"

        matched = match_jobs_to_resume(jobs, resume_text, top_k=2)

        self.assertEqual(len(matched), 2)
        # Check that similarity scores are added
        for job in matched:
            self.assertIn('similarity', job)
            self.assertIsInstance(job['similarity'], float)

        # Python job should have higher similarity than Java job
        python_job = next(job for job in matched if 'Python' in job['title'])
        java_job = next(job for job in matched if 'Java' in job['title'])
        self.assertGreater(python_job['similarity'], java_job['similarity'])


class ViewTests(TestCase):
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_home_view(self):
        """Test home page view."""
        response = self.client.get(reverse('jobapp:home'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Job Hunter')

    def test_job_search_view(self):
        """Test job search page view."""
        response = self.client.get(reverse('jobapp:job_search'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Search for Jobs')

    def test_resume_list_requires_login(self):
        """Test that resume list requires authentication."""
        response = self.client.get(reverse('jobapp:resume_list'))
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_resume_list_authenticated(self):
        """Test resume list view when authenticated."""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('jobapp:resume_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'My Resumes')

    def test_application_list_requires_login(self):
        """Test that application list requires authentication."""
        response = self.client.get(reverse('jobapp:application_list'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
