# generator.py

from typing import Dict
import openai
import os

openai.api_key = os.getenv("OPENAI_API_KEY")


def generate_cover_letter(job: Dict, resume_text: str) -> str:
    prompt = f"""
    Write a personalized cover letter for the following job and resume.

    Job Title: {job['title']}
    Company: {job['company']}
    Location: {job['location']}
    Job Description: {job.get('description', '')}

    Resume:
    {resume_text}

    Cover Letter:
    """
    
    response = openai.Completion.create(
        engine="gpt-4",
        prompt=prompt,
        temperature=0.7,
        max_tokens=500
    )
    return response.choices[0].text.strip()
