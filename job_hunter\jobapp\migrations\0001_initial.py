# Generated by Django 5.2.4 on 2025-07-13 23:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Job',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=300)),
                ('company', models.CharField(max_length=200)),
                ('location', models.CharField(max_length=200)),
                ('url', models.URLField(unique=True)),
                ('description', models.TextField(blank=True)),
                ('keywords', models.JSONField(default=list, help_text='Extracted keywords from job description')),
                ('salary_min', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('salary_max', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('job_type', models.CharField(blank=True, max_length=50)),
                ('experience_level', models.CharField(blank=True, max_length=50)),
                ('scraped_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['-scraped_at'],
                'indexes': [models.Index(fields=['company'], name='jobapp_job_company_210738_idx'), models.Index(fields=['location'], name='jobapp_job_locatio_3088ae_idx'), models.Index(fields=['scraped_at'], name='jobapp_job_scraped_dc18b2_idx')],
            },
        ),
        migrations.CreateModel(
            name='Resume',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField(help_text='Full text content of the resume')),
                ('file', models.FileField(blank=True, null=True, upload_to='resumes/')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this resume is currently active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resumes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='SearchHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('keywords', models.CharField(max_length=500)),
                ('locations', models.CharField(max_length=500)),
                ('results_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='search_history', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Search histories',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('linkedin_profile', models.URLField(blank=True)),
                ('github_profile', models.URLField(blank=True)),
                ('portfolio_website', models.URLField(blank=True)),
                ('preferred_locations', models.TextField(help_text='Comma-separated list of preferred job locations')),
                ('preferred_keywords', models.TextField(help_text='Comma-separated list of preferred job keywords')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='JobApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('saved', 'Saved'), ('applied', 'Applied'), ('interview', 'Interview Scheduled'), ('rejected', 'Rejected'), ('offer', 'Offer Received'), ('accepted', 'Offer Accepted'), ('declined', 'Offer Declined')], default='saved', max_length=20)),
                ('similarity_score', models.FloatField(blank=True, help_text='Resume-job similarity score', null=True)),
                ('cover_letter', models.TextField(blank=True)),
                ('notes', models.TextField(blank=True)),
                ('applied_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='jobapp.job')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to=settings.AUTH_USER_MODEL)),
                ('resume', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='jobapp.resume')),
            ],
            options={
                'ordering': ['-updated_at'],
                'unique_together': {('user', 'job')},
            },
        ),
    ]
