{% extends 'jobapp/base.html' %}

{% block title %}My Applications - Job Hunter{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2>
                <i class="fas fa-clipboard-list"></i> My Applications
            </h2>
            <a href="{% url 'jobapp:job_search' %}" class="btn btn-primary">
                <i class="fas fa-search"></i> Find More Jobs
            </a>
        </div>
    </div>
</div>

<!-- Status Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex flex-wrap gap-2">
                    <a href="{% url 'jobapp:application_list' %}" 
                       class="btn btn-sm {% if not current_status %}btn-primary{% else %}btn-outline-primary{% endif %}">
                        All
                    </a>
                    {% for status_code, status_name in status_choices %}
                    <a href="{% url 'jobapp:application_list' %}?status={{ status_code }}" 
                       class="btn btn-sm {% if current_status == status_code %}btn-primary{% else %}btn-outline-primary{% endif %}">
                        {{ status_name }}
                    </a>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

{% if page_obj %}
<div class="row">
    {% for application in page_obj %}
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h5 class="card-title">
                        <a href="{% url 'jobapp:application_detail' application.id %}" 
                           class="text-decoration-none">
                            {{ application.job.title }}
                        </a>
                    </h5>
                    <span class="badge bg-{% if application.status == 'applied' %}success{% elif application.status == 'interview' %}warning{% elif application.status == 'offer' %}info{% elif application.status == 'rejected' %}danger{% else %}secondary{% endif %}">
                        {{ application.get_status_display }}
                    </span>
                </div>
                
                <h6 class="card-subtitle mb-2 text-muted">
                    <i class="fas fa-building"></i> {{ application.job.company }}
                </h6>
                
                <p class="card-text">
                    <i class="fas fa-map-marker-alt"></i> {{ application.job.location }}
                </p>
                
                {% if application.similarity_score %}
                <div class="mb-2">
                    <small class="text-muted">Match Score:</small>
                    <span class="similarity-score">
                        {{ application.similarity_score|floatformat:1 }}%
                    </span>
                </div>
                {% endif %}
                
                <div class="mb-3">
                    <small class="text-muted">
                        <i class="fas fa-file-alt"></i> Resume: {{ application.resume.title }}
                        <br>
                        <i class="fas fa-calendar"></i> 
                        {% if application.applied_date %}
                            Applied: {{ application.applied_date|date:"M d, Y" }}
                        {% else %}
                            Saved: {{ application.created_at|date:"M d, Y" }}
                        {% endif %}
                    </small>
                </div>
                
                {% if application.notes %}
                <p class="card-text">
                    <small>{{ application.notes|truncatewords:15 }}</small>
                </p>
                {% endif %}
                
                <div class="d-flex justify-content-between align-items-center">
                    <div class="btn-group" role="group">
                        <a href="{% url 'jobapp:application_detail' application.id %}" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="{{ application.job.url }}" 
                           target="_blank" 
                           class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-external-link-alt"></i> Job
                        </a>
                    </div>
                    
                    <small class="text-muted">
                        Updated {{ application.updated_at|timesince }} ago
                    </small>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if page_obj.has_other_pages %}
<div class="row">
    <div class="col-12">
        <nav aria-label="Applications pagination">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if current_status %}&status={{ current_status }}{% endif %}">First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if current_status %}&status={{ current_status }}{% endif %}">Previous</a>
                    </li>
                {% endif %}
                
                <li class="page-item active">
                    <span class="page-link">
                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>
                </li>
                
                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if current_status %}&status={{ current_status }}{% endif %}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if current_status %}&status={{ current_status }}{% endif %}">Last</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>
{% endif %}

{% else %}
<div class="row">
    <div class="col-lg-8 mx-auto text-center">
        <div class="card">
            <div class="card-body">
                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                <h4>No applications found</h4>
                <p class="text-muted">
                    {% if current_status %}
                        No applications with status "{{ current_status|title }}" found.
                    {% else %}
                        You haven't saved any job applications yet. Start by searching for jobs!
                    {% endif %}
                </p>
                <a href="{% url 'jobapp:job_search' %}" class="btn btn-primary">
                    <i class="fas fa-search"></i> Search for Jobs
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
