from django.shortcuts import render

# Create your views here.
# linkedin_scraper_app/views.py

import csv
import random
import sqlite3
import logging
from io import StringIO
from django.http import HttpResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

LOGGER = logging.getLogger(__name__)
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64)...',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 13_4)...',
    'Mozilla/5.0 (X11; Linux x86_64)...',
]
CACHE_DB = 'jobs_cache.db'
SERVICE = Service()


def init_db():
    conn = sqlite3.connect(CACHE_DB)
    c = conn.cursor()
    c.execute('CREATE TABLE IF NOT EXISTS scraped_urls (url TEXT PRIMARY KEY)')
    conn.commit()
    conn.close()


def is_cached(url):
    conn = sqlite3.connect(CACHE_DB)
    c = conn.cursor()
    c.execute('SELECT 1 FROM scraped_urls WHERE url=?', (url,))
    result = c.fetchone() is not None
    conn.close()
    return result


def cache_url(url):
    conn = sqlite3.connect(CACHE_DB)
    c = conn.cursor()
    c.execute('INSERT OR IGNORE INTO scraped_urls (url) VALUES (?)', (url,))
    conn.commit()
    conn.close()


def scrape_jobs(keywords, locations, pages):
    jobs = []
    ua = random.choice(USER_AGENTS)
    LOGGER.info(f'Using UA: {ua}')
    opts = Options()
    opts.add_argument(f'--user-agent={ua}')
    opts.add_argument('--disable-gpu')
    # opts.add_argument('--headless=new')
    driver = webdriver.Chrome(service=SERVICE, options=opts)
    wait = WebDriverWait(driver, 10)

    for kw in keywords:
        for loc in locations:
            url = f"https://www.linkedin.com/jobs/search/?keywords={kw}&location={loc}&f_TP=1%2C2&f_E=2%2C3"
            driver.get(url)
            wait.until(EC.presence_of_element_located((By.CLASS_NAME, 'jobs-search-results__list-item')))
            for _ in range(pages):
                driver.execute_script('window.scrollTo(0, document.body.scrollHeight)')
                wait.until(EC.presence_of_all_elements_located((By.CLASS_NAME, 'jobs-search-results__list-item')))
                items = driver.find_elements(By.CLASS_NAME, 'jobs-search-results__list-item')
                for el in items:
                    try:
                        link = el.find_element(By.TAG_NAME, 'a').get_attribute('href')
                        if is_cached(link):
                            continue
                        title = el.find_element(By.CSS_SELECTOR, 'h3').text
                        comp = el.find_element(By.CSS_SELECTOR, 'h4').text
                        loc_text = el.find_element(By.CSS_SELECTOR, '.job-search-card__location').text
                        jobs.append({
                            'keyword': kw,
                            'title': title,
                            'company': comp,
                            'location': loc_text,
                            'url': link
                        })
                        cache_url(link)
                    except Exception as e:
                        LOGGER.warning(e)
                try:
                    nxt = driver.find_element(By.CLASS_NAME, 'artdeco-pagination__button--next')
                    if 'disabled' in nxt.get_attribute('class'):
                        break
                    nxt.click()
                    wait.until(EC.staleness_of(nxt))
                except:
                    break
    driver.quit()
    return jobs


@csrf_exempt
def scrape_view(request):
    if request.method == 'POST':
        keywords = request.POST.get('keywords', '').split(',')
        locations = request.POST.get('locations', '').split(',')
        pages = int(request.POST.get('pages', 2))
        jobs = scrape_jobs(keywords, locations, pages)
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="jobs.csv"'
        writer = csv.DictWriter(response, fieldnames=['keyword', 'title', 'company', 'location', 'url'])
        writer.writeheader()
        writer.writerows(jobs)
        return response
    return render(request, 'scraper_form.html')
