# analyzer.py

import spacy
from typing import List, Dict
from collections import Counter

nlp = spacy.load("en_core_web_sm")


def extract_keywords(text: str, top_n: int = 10) -> List[str]:
    doc = nlp(text.lower())
    tokens = [token.lemma_ for token in doc if token.pos_ in ("NOUN", "PROPN", "VERB") and not token.is_stop]
    freq = Counter(tokens)
    return [word for word, _ in freq.most_common(top_n)]


def analyze_jobs(jobs: List[Dict], top_n: int = 10) -> List[Dict]:
    for job in jobs:
        combined = job['title'] + ' ' + job.get('description', '')
        job['keywords'] = extract_keywords(combined, top_n=top_n)
    return jobs
