# analyzer.py

import re
from typing import List, Dict
from collections import Counter

# Common technical keywords to prioritize
TECH_KEYWORDS = {
    'python', 'java', 'javascript', 'react', 'angular', 'vue', 'node', 'django', 'flask',
    'sql', 'mysql', 'postgresql', 'mongodb', 'redis', 'aws', 'azure', 'gcp', 'docker',
    'kubernetes', 'git', 'linux', 'windows', 'api', 'rest', 'graphql', 'microservices',
    'machine learning', 'ai', 'data science', 'analytics', 'tensorflow', 'pytorch',
    'html', 'css', 'bootstrap', 'sass', 'webpack', 'npm', 'yarn', 'typescript',
    'c++', 'c#', 'go', 'rust', 'php', 'ruby', 'swift', 'kotlin', 'scala', 'r',
    'agile', 'scrum', 'devops', 'ci/cd', 'jenkins', 'github', 'gitlab', 'jira'
}

# Simple stopwords
STOPWORDS = {
    'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from', 'has', 'he',
    'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the', 'to', 'was', 'will', 'with',
    'i', 'you', 'we', 'they', 'this', 'these', 'those', 'or', 'but', 'if', 'then',
    'can', 'could', 'should', 'would', 'may', 'might', 'must', 'shall', 'do', 'does',
    'did', 'have', 'had', 'been', 'being', 'am', 'were', 'my', 'your', 'his', 'her',
    'our', 'their', 'me', 'him', 'us', 'them', 'what', 'when', 'where', 'who', 'why',
    'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such',
    'work', 'working', 'experience', 'years', 'team', 'company', 'role', 'position',
    'job', 'career', 'opportunity', 'looking', 'seeking', 'required', 'preferred'
}


def extract_keywords(text: str, top_n: int = 10) -> List[str]:
    """Extract keywords from text using simple frequency analysis."""
    if not text:
        return []

    # Convert to lowercase
    text = text.lower()

    # Remove punctuation except hyphens and plus signs (for tech terms like C++, .NET)
    text = re.sub(r'[^\w\s\-\+\.]', ' ', text)

    # Split into words
    words = text.split()

    # Filter words
    filtered_words = []
    for word in words:
        # Remove stopwords and short words
        if word not in STOPWORDS and len(word) > 2:
            # Prioritize technical keywords
            if word in TECH_KEYWORDS:
                filtered_words.extend([word] * 3)  # Give tech keywords more weight
            else:
                filtered_words.append(word)

    # Count frequency
    freq = Counter(filtered_words)

    # Return top keywords
    return [word for word, _ in freq.most_common(top_n)]


def analyze_jobs(jobs: List[Dict], top_n: int = 10) -> List[Dict]:
    """Analyze jobs and extract keywords."""
    for job in jobs:
        combined = job['title'] + ' ' + job.get('description', '')
        job['keywords'] = extract_keywords(combined, top_n=top_n)
    return jobs
